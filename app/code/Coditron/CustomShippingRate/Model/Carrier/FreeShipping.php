<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Model\Carrier;

use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Shipping\Model\Carrier\AbstractCarrier;
use Magento\Shipping\Model\Carrier\CarrierInterface;
use Magento\Shipping\Model\Rate\Result;
use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;

/**
 * Free Shipping Carrier for Custom Shipping Rate Thresholds
 */
class FreeShipping extends AbstractCarrier implements CarrierInterface
{
    /**
     * Carrier's code
     *
     * @var string
     */
    protected $_code = 'customfreeshipping';

    /**
     * Whether this carrier has fixed rates calculation
     *
     * @var bool
     */
    protected $_isFixed = true;

    /**
     * @var \Magento\Shipping\Model\Rate\ResultFactory
     */
    protected $rateResultFactory;

    /**
     * @var \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory
     */
    protected $rateMethodFactory;

    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @var ShipTableRatesRepositoryInterface
     */
    protected $shipTableRatesRepository;

    /**
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Shipping\Model\Rate\ResultFactory $rateResultFactory
     * @param \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Shipping\Model\Rate\ResultFactory $rateResultFactory,
        \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory,
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        ShipTableRatesRepositoryInterface $shipTableRatesRepository,
        array $data = []
    ) {
        $this->rateResultFactory = $rateResultFactory;
        $this->rateMethodFactory = $rateMethodFactory;
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        $this->shipTableRatesRepository = $shipTableRatesRepository;
        parent::__construct($scopeConfig, $rateErrorFactory, $logger, $data);
    }

    /**
     * Collect and get rates for free shipping thresholds
     *
     * @param RateRequest $request
     * @return Result|bool
     */
    public function collectRates(RateRequest $request)
    {
        if (!$this->getConfigFlag('active')) {
            return false;
        }

        $result = $this->rateResultFactory->create();
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        // Get available free shipping thresholds for this country
        $thresholds = $this->getAvailableThresholds($country, $subtotal);

        // Only show free shipping if there are valid thresholds
        if (empty($thresholds)) {
            return false;
        }

        foreach ($thresholds as $threshold) {
            $method = $this->rateMethodFactory->create();
            $method->setCarrier($this->_code);
            $method->setCarrierTitle($this->getConfigData('title') ?: 'Free Shipping');
            $method->setMethod('threshold_' . $threshold->getShiptableratesId());
            $method->setMethodTitle($this->getThresholdMethodTitle($threshold));
            $method->setPrice(0);
            $method->setCost(0);

            $result->append($method);
        }

        return $result;
    }

    /**
     * Get available free shipping thresholds for country and subtotal
     *
     * @param string $country
     * @param float $subtotal
     * @return \Coditron\CustomShippingRate\Model\ShipTableRates[]
     */
    protected function getAvailableThresholds($country, $subtotal)
    {
        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);

        $availableThresholds = [];
        foreach ($collection as $threshold) {
            $countries = $threshold->getCountries();
            if (in_array($country, $countries)) {
                $availableThresholds[] = $threshold;
            }
        }

        return $availableThresholds;
    }

    /**
     * Get method title for threshold
     *
     * @param \Coditron\CustomShippingRate\Model\ShipTableRates $threshold
     * @return string
     */
    protected function getThresholdMethodTitle($threshold)
    {
        $courierName = $threshold->getCourier() ?: 'Free Shipping';
        $minAmount = $threshold->getMinOrderAmount();

        if ($minAmount > 0) {
            return $courierName . ' (For orders over $' . number_format($minAmount, 2) . ')';
        }

        return $courierName;
    }

    /**
     * Get allowed methods
     *
     * @return array
     */
    public function getAllowedMethods()
    {
        return ['freeshipping' => $this->getConfigData('name')];
    }
}
