<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Quote\Address\Total;

use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Address;
use Magento\Quote\Model\Quote\Address\Rate;
use Magento\Quote\Model\Quote\Address\Total;
use Magento\Quote\Model\Quote\Address\Total\Shipping;
use Coditron\CustomShippingRate\Helper\Data;
use Coditron\CustomShippingRate\Model\Carrier;
use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;

class ShippingPlugin
{
    /**
     * @var Data
     */
    protected $customShippingRateHelper;

    /**
     * @var ShipTableRatesRepositoryInterface
     */
    protected $shipTableRatesRepository;

    /**
     * @param Data $customShippingRateHelper
     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
     */
    public function __construct(
        Data $customShippingRateHelper,
        ShipTableRatesRepositoryInterface $shipTableRatesRepository
    ) {
        $this->customShippingRateHelper = $customShippingRateHelper;
        $this->shipTableRatesRepository = $shipTableRatesRepository;
    }

    /**
     * @param Shipping $subject
     * @param callable $proceed
     * @param Quote $quote
     * @param ShippingAssignmentInterface $shippingAssignment
     * @param Total $total
     * @return mixed
     */
    public function aroundCollect(
        Shipping $subject,
        callable $proceed,
        Quote $quote,
        ShippingAssignmentInterface $shippingAssignment,
        Total $total
    ) {
        $shipping = $shippingAssignment->getShipping();
        $address = $shipping->getAddress();
        $method = $address->getShippingMethod();
        $storeId = $quote->getStoreId();

        if (!$this->customShippingRateHelper->isEnabled($storeId)
            || $address->getAddressType() != Address::ADDRESS_TYPE_SHIPPING
            || (strpos((string) $method, Carrier::CODE) === false && strpos((string) $method, 'freeshipping') === false)
        ) {
            return $proceed($quote, $shippingAssignment, $total);
        }

        $customShippingOption = $this->getCustomShippingJsonToArray($method, $address, $storeId);

        if ($customShippingOption && strpos((string) $method, $customShippingOption['code']) !== false) {
            //update shipping code
            $shipping->setMethod($customShippingOption['code']);
            $address->setShippingMethod($customShippingOption['code']);

            $this->updateCustomRate($address, $customShippingOption);

            // For free shipping methods, ensure totals are set to zero
            if (strpos($method, 'freeshipping') === 0) {
                $total->setShippingAmount(0);
                $total->setBaseShippingAmount(0);
                $total->setShippingDescription($customShippingOption['description'] ?? 'Free Shipping');
            }
        }

        return $proceed($quote, $shippingAssignment, $total);
    }

    /**
     * @param $address
     * @param $customShippingOption
     */
    protected function updateCustomRate($address, $customShippingOption)
    {
        if ($selectedRate = $this->getSelectedShippingRate($address, $customShippingOption['code'])) {
            $cost = (float) $customShippingOption['rate'];
            $description = trim($customShippingOption['description']);

            $selectedRate->setPrice($cost);
            $selectedRate->setCost($cost);
            //Empty by default. Use in third-party modules
            if (!empty($description) || strlen($description) > 2) {
                $selectedRate->setMethodTitle($description);
                //TODO: check if the above has any effect / if passed by reference.. or gets lost
            }
        }
    }

    /**
     * @param $json
     * @param $address
     * @param null $storeId
     * @return array|bool
     */
    private function getCustomShippingJsonToArray($json, $address, $storeId = null)
    {
        $isJson = $this->customShippingRateHelper->isJson($json);

        //reload exist shipping cost if custom shipping method
        if ($json && !$isJson) {
            $rate = 0;
            if ($selectedRate = $this->getSelectedShippingRate($address, $json)) {
                $rate = $selectedRate->getPrice();
            }

            // Handle free shipping codes
            if (strpos($json, 'freeshipping') === 0) {
                $jsonToArray = [
                    'code' => $json,
                    'type' => 'free_shipping',
                    'rate' => 0, // Free shipping
                    'description' => 'Free Shipping'
                ];
            } else {
                $jsonToArray = [
                    'code' => $json,
                    'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
                    'rate' => $rate
                ];
            }

            return $this->formatShippingArray($jsonToArray);
        }

        $jsonToArray = (array)json_decode($json, true);

        if (is_array($jsonToArray) && count($jsonToArray) == 4) {
            return $this->formatShippingArray($jsonToArray);
        }

        return false;
    }

    /**
     * @param $address
     * @param $code
     * @return null | Rate
     */
    protected function getSelectedShippingRate($address, $code)
    {
        $selectedRate = null;

        if ($code) {
            foreach ($address->getAllShippingRates() as $rate) {
                if ($rate->getCode() == $code) {
                    $selectedRate = $rate;
                    break;
                }
            }
        }

        return $selectedRate;
    }



    /**
     * @param $jsonToArray array
     * @return array
     */
    protected function formatShippingArray($jsonToArray)
    {
        $customShippingOption = [
            'code' => '',
            'rate' => 0,
            'type' => '',
            'description' => ''
        ];

        foreach ((array) $jsonToArray as $key => $value) {
            $customShippingOption[$key] = $value;
        }

        return $customShippingOption;
    }
}
