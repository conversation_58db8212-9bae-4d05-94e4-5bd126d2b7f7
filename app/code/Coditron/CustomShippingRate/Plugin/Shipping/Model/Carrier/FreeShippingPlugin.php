<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\OfflineShipping\Model\Carrier\Freeshipping;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
use Psr\Log\LoggerInterface;

/**
 * Plugin to conditionally enable core freeshipping when thresholds exist and are met
 */
class FreeShippingPlugin
{
    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        LoggerInterface $logger
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        $this->logger = $logger;
        $this->logger->info('[FreeShippingPlugin] Plugin instantiated');
    }

    /**
     * Log before collectRates is called
     *
     * @param Freeshipping $subject
     * @param RateRequest $request
     * @return array|null
     */
    public function beforeCollectRates(Freeshipping $subject, RateRequest $request)
    {
        $this->logger->info('[FreeShippingPlugin] beforeCollectRates called', [
            'country' => $request->getDestCountryId(),
            'subtotal' => $request->getBaseSubtotalInclTax()
        ]);
        return null;
    }

    /**
     * Enable core freeshipping only when thresholds exist and are met for the country
     *
     * @param Freeshipping $subject
     * @param callable $proceed
     * @param RateRequest $request
     * @return \Magento\Shipping\Model\Rate\Result|bool
     */
    public function aroundCollectRates(Freeshipping $subject, callable $proceed, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        $this->logger->info('[FreeShippingPlugin] Checking thresholds', [
            'country' => $country,
            'subtotal' => $subtotal
        ]);

        // Check if there are any free shipping thresholds for this country that are met
        $metThresholds = $this->getMetThresholdsForCountry($country, $subtotal);

        $this->logger->info('[FreeShippingPlugin] Threshold check result', [
            'hasMetThresholds' => !empty($metThresholds)
        ]);

        if (empty($metThresholds)) {
            // Return false to prevent core freeshipping from showing
            $this->logger->info('[FreeShippingPlugin] Blocking free shipping - no thresholds met');
            return false;
        }

        // Temporarily override the free shipping subtotal to enable it
        $this->overrideFreeShippingConfig($subject, $metThresholds);

        // Allow normal processing if thresholds are met
        $this->logger->info('[FreeShippingPlugin] Allowing free shipping - thresholds met');
        $result = $proceed($request);

        // Modify the method title to include threshold information
        if ($result && $result->getAllRates()) {
            $this->addThresholdSubtitle($result, $metThresholds);
        }

        return $result;
    }

    /**
     * Get free shipping thresholds for the given country that are met
     *
     * @param string $country
     * @param float $subtotal
     * @return array
     */
    protected function getMetThresholdsForCountry($country, $subtotal)
    {
        if (!$country) {
            $this->logger->info('[FreeShippingPlugin] No country provided');
            return [];
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);

        $this->logger->info('[FreeShippingPlugin] Collection query', [
            'count' => $collection->getSize(),
            'sql' => $collection->getSelect()->__toString()
        ]);

        $metThresholds = [];
        foreach ($collection as $threshold) {
            $countries = $threshold->getCountries();
            $this->logger->info('[FreeShippingPlugin] Checking threshold', [
                'threshold_id' => $threshold->getShiptableratesId(),
                'countries' => $countries,
                'min_amount' => $threshold->getMinOrderAmount(),
                'target_country' => $country
            ]);

            if (in_array($country, $countries)) {
                $this->logger->info('[FreeShippingPlugin] Found matching threshold');
                $metThresholds[] = $threshold;
            }
        }

        $this->logger->info('[FreeShippingPlugin] Found thresholds count', [
            'count' => count($metThresholds)
        ]);

        return $metThresholds;
    }

    /**
     * Override free shipping configuration to enable it
     *
     * @param Freeshipping $subject
     * @param array $metThresholds
     * @return void
     */
    protected function overrideFreeShippingConfig(Freeshipping $subject, $metThresholds)
    {
        // Get the lowest threshold to set as the free shipping subtotal
        $lowestThreshold = null;
        foreach ($metThresholds as $threshold) {
            if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
                $lowestThreshold = $threshold->getMinOrderAmount();
            }
        }

        // Use reflection to temporarily override the config data
        $reflection = new \ReflectionClass($subject);
        if ($reflection->hasProperty('_configData')) {
            $configProperty = $reflection->getProperty('_configData');
            $configProperty->setAccessible(true);
            $configData = $configProperty->getValue($subject) ?: [];

            // Set the free shipping subtotal to our threshold
            $configData['free_shipping_subtotal'] = $lowestThreshold;
            $configProperty->setValue($subject, $configData);

            $this->logger->info('[FreeShippingPlugin] Overrode free shipping subtotal', [
                'threshold' => $lowestThreshold
            ]);
        }
    }

    /**
     * Add threshold subtitle to free shipping method
     *
     * @param \Magento\Shipping\Model\Rate\Result $result
     * @param array $metThresholds
     * @return void
     */
    protected function addThresholdSubtitle($result, $metThresholds)
    {
        if (empty($metThresholds)) {
            return;
        }

        // Get the lowest threshold amount to show
        $lowestThreshold = null;
        foreach ($metThresholds as $threshold) {
            if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
                $lowestThreshold = $threshold->getMinOrderAmount();
            }
        }

        // Modify all free shipping rates to include the subtitle in plain text
        foreach ($result->getAllRates() as $rate) {
            if ($rate->getCarrier() === 'freeshipping') {
                $currentTitle = $rate->getMethodTitle();
                $subtitle = sprintf('(For orders over $%.2f)', $lowestThreshold);

                // Add subtitle to the method title as plain text
                $newTitle = $currentTitle . ' ' . $subtitle;
                $rate->setMethodTitle($newTitle);

                $this->logger->info('[FreeShippingPlugin] Added subtitle to free shipping', [
                    'original_title' => $currentTitle,
                    'new_title' => $newTitle,
                    'threshold' => $lowestThreshold
                ]);
            }
        }
    }
}
