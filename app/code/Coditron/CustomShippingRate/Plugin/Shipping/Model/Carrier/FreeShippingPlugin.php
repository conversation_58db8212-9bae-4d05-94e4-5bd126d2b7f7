<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\Shipping\Model\Carrier\Free;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
use Psr\Log\LoggerInterface;

/**
 * Plugin to conditionally enable core freeshipping when thresholds exist and are met
 */
class FreeShippingPlugin
{
    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        LoggerInterface $logger
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        $this->logger = $logger;
    }

    /**
     * Enable core freeshipping only when thresholds exist and are met for the country
     *
     * @param Free $subject
     * @param callable $proceed
     * @param RateRequest $request
     * @return \Magento\Shipping\Model\Rate\Result|bool
     */
    public function aroundCollectRates(Free $subject, callable $proceed, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        $this->logger->info('[FreeShippingPlugin] Checking thresholds', [
            'country' => $country,
            'subtotal' => $subtotal
        ]);

        // Check if there are any free shipping thresholds for this country that are met
        $hasMetThresholds = $this->hasMetThresholdsForCountry($country, $subtotal);

        $this->logger->info('[FreeShippingPlugin] Threshold check result', [
            'hasMetThresholds' => $hasMetThresholds
        ]);

        if (!$hasMetThresholds) {
            // Return false to prevent core freeshipping from showing
            $this->logger->info('[FreeShippingPlugin] Blocking free shipping - no thresholds met');
            return false;
        }

        // Allow normal processing if thresholds are met
        $this->logger->info('[FreeShippingPlugin] Allowing free shipping - thresholds met');
        return $proceed($request);
    }

    /**
     * Check if there are free shipping thresholds for the given country that are met
     *
     * @param string $country
     * @param float $subtotal
     * @return bool
     */
    protected function hasMetThresholdsForCountry($country, $subtotal)
    {
        if (!$country) {
            $this->logger->info('[FreeShippingPlugin] No country provided');
            return false;
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);

        $this->logger->info('[FreeShippingPlugin] Collection query', [
            'count' => $collection->getSize(),
            'sql' => $collection->getSelect()->__toString()
        ]);

        foreach ($collection as $threshold) {
            $countries = $threshold->getCountries();
            $this->logger->info('[FreeShippingPlugin] Checking threshold', [
                'threshold_id' => $threshold->getShiptableratesId(),
                'countries' => $countries,
                'min_amount' => $threshold->getMinOrderAmount(),
                'target_country' => $country
            ]);

            if (in_array($country, $countries)) {
                $this->logger->info('[FreeShippingPlugin] Found matching threshold');
                return true;
            }
        }

        $this->logger->info('[FreeShippingPlugin] No matching thresholds found');
        return false;
    }
}
