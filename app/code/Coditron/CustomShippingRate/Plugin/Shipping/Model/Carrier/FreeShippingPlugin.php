<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\Shipping\Model\Carrier\Free;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;

/**
 * Plugin to conditionally enable core freeshipping when thresholds exist and are met
 */
class FreeShippingPlugin
{
    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     */
    public function __construct(
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
    }

    /**
     * Enable core freeshipping only when thresholds exist and are met for the country
     *
     * @param Free $subject
     * @param RateRequest $request
     * @return array|null
     */
    public function beforeCollectRates(Free $subject, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        $subtotal = $request->getBaseSubtotalInclTax();

        // Check if there are any free shipping thresholds for this country that are met
        if (!$this->hasMetThresholdsForCountry($country, $subtotal)) {
            // Return false to prevent core freeshipping from collecting rates
            return [null]; // This will make collectRates return false
        }

        return null; // Allow normal processing
    }

    /**
     * Check if there are free shipping thresholds for the given country that are met
     *
     * @param string $country
     * @param float $subtotal
     * @return bool
     */
    protected function hasMetThresholdsForCountry($country, $subtotal)
    {
        if (!$country) {
            return false;
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);

        foreach ($collection as $threshold) {
            $countries = $threshold->getCountries();
            if (in_array($country, $countries)) {
                return true;
            }
        }

        return false;
    }
}
