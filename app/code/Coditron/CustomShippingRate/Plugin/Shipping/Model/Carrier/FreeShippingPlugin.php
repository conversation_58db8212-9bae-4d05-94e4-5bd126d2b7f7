<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;

use Magento\Shipping\Model\Carrier\Free;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;

/**
 * Plugin to conditionally disable core freeshipping when custom thresholds exist
 */
class FreeShippingPlugin
{
    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     */
    public function __construct(
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
    }

    /**
     * Disable core freeshipping when custom thresholds exist for the country
     *
     * @param Free $subject
     * @param RateRequest $request
     * @return array|null
     */
    public function beforeCollectRates(Free $subject, RateRequest $request)
    {
        $country = $request->getDestCountryId();
        
        // Check if there are any free shipping thresholds for this country
        if ($this->hasThresholdsForCountry($country)) {
            // Return false to prevent core freeshipping from collecting rates
            return [null]; // This will make collectRates return false
        }

        return null; // Allow normal processing
    }

    /**
     * Check if there are free shipping thresholds for the given country
     *
     * @param string $country
     * @return bool
     */
    protected function hasThresholdsForCountry($country)
    {
        if (!$country) {
            return false;
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0]);

        foreach ($collection as $threshold) {
            $countries = $threshold->getCountries();
            if (in_array($country, $countries)) {
                return true;
            }
        }

        return false;
    }
}
