diff --git a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
index 5529cd649..202b35f6f 100644
--- a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
+++ b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
@@ -18,6 +18,7 @@ interface ShipTableRatesInterface
     const WEIGHT = 'weight';
     const SHIPPING_PRICE = 'shipping_price';
     const FREE_SHIPPING = 'free_shipping';
+    const MIN_ORDER_AMOUNT = 'min_order_amount';
 
     /**
      * Get shiptablerates_id
@@ -175,5 +176,18 @@ interface ShipTableRatesInterface
      */
     public function setFreeShipping(bool $freeShipping): ShipTableRatesInterface;
 
+    /**
+     * Get minimum order amount
+     * @return float|null
+     */
+    public function getMinOrderAmount(): ?float;
+
+    /**
+     * Set minimum order amount
+     * @param float $minOrderAmount
+     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface;
+
 }
 
diff --git a/app/code/Coditron/CustomShippingRate/Block/Adminhtml/Order/Create/Shipping/Method/Form.php b/app/code/Coditron/CustomShippingRate/Block/Adminhtml/Order/Create/Shipping/Method/Form.php
index 8da1d0ef4..2e267f39c 100755
--- a/app/code/Coditron/CustomShippingRate/Block/Adminhtml/Order/Create/Shipping/Method/Form.php
+++ b/app/code/Coditron/CustomShippingRate/Block/Adminhtml/Order/Create/Shipping/Method/Form.php
@@ -9,6 +9,7 @@ namespace Coditron\CustomShippingRate\Block\Adminhtml\Order\Create\Shipping\Meth
 
 use Magento\Quote\Model\Quote\Address\Rate;
 use Coditron\CustomShippingRate\Model\Carrier;
+use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
 
 /**
  * Class Form
@@ -19,6 +20,33 @@ class Form extends \Magento\Sales\Block\Adminhtml\Order\Create\Shipping\Method\F
     /** @var Rate|false **/
     protected $activeMethodRate;
 
+    /** @var ShipTableRatesCollectionFactory */
+    protected $shipTableRatesCollectionFactory;
+
+    /**
+     * Constructor
+     *
+     * @param \Magento\Backend\Block\Template\Context $context
+     * @param \Magento\Backend\Model\Session\Quote $sessionQuote
+     * @param \Magento\Sales\Model\AdminOrder\Create $orderCreate
+     * @param \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency
+     * @param \Magento\Tax\Helper\Data $taxData
+     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
+     * @param array $data
+     */
+    public function __construct(
+        \Magento\Backend\Block\Template\Context $context,
+        \Magento\Backend\Model\Session\Quote $sessionQuote,
+        \Magento\Sales\Model\AdminOrder\Create $orderCreate,
+        \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency,
+        \Magento\Tax\Helper\Data $taxData,
+        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
+        array $data = []
+    ) {
+        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
+        parent::__construct($context, $sessionQuote, $orderCreate, $priceCurrency, $taxData, $data);
+    }
+
     /**
      * Custom shipping rate
      *
@@ -79,4 +107,35 @@ class Form extends \Magento\Sales\Block\Adminhtml\Order\Create\Shipping\Method\F
 
         return $rates;
     }
+
+    /**
+     * Get available free shipping thresholds
+     *
+     * @return array
+     */
+    public function getFreeShippingThresholds()
+    {
+        $orderCountry = $this->getQuote()->getShippingAddress()->getCountryId();
+
+        $collection = $this->shipTableRatesCollectionFactory->create();
+        $collection->addFieldToFilter('free_shipping', 1)
+                   ->addFieldToFilter('min_order_amount', ['gt' => 0]);
+
+        $thresholds = [];
+        foreach ($collection as $threshold) {
+            $countries = $threshold->getCountries();
+            if (in_array($orderCountry, $countries)) {
+                $thresholds[] = [
+                    'id' => $threshold->getShiptableratesId(),
+                    'courier' => $threshold->getCourier(),
+                    'service_type' => $threshold->getServiceType(),
+                    'countries' => $threshold->getCountries(),
+                    'min_amount' => $threshold->getMinOrderAmount(),
+                    'seller_id' => $threshold->getSellerId()
+                ];
+            }
+        }
+
+        return $thresholds;
+    }
 }
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
index a848ecfba..05161991b 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
@@ -51,12 +51,24 @@ class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 
                 $sellerShiprate->save();
                 $id = $sellerShiprate->getShiptableratesId();
-                $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
-                $this->_helper->clearCache();
-                return $this->resultRedirectFactory->create()->setPath(
-                    'coditron_customshippingrate/shiptablerates/edit',
-                    ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
-                );
+
+                // Check if this is a threshold save
+                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+                if ($isThreshold) {
+                    $this->messageManager->addSuccess(__("Free Shipping Threshold saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/editthreshold',
+                        ['id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                } else {
+                    $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/edit',
+                        ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                }
             } catch (\Exception $e) {
                 $this->messageManager->addError($e->getMessage());
                 return $this->resultRedirectFactory->create()->setPath(
diff --git a/app/code/Coditron/CustomShippingRate/Model/Carrier.php b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
index 8d1ad90e1..344c44b63 100644
--- a/app/code/Coditron/CustomShippingRate/Model/Carrier.php
+++ b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
@@ -158,6 +158,11 @@ class Carrier extends AbstractCarrier implements CarrierInterface
                     continue;
                 }
 
+                // Skip free shipping thresholds from regular rates
+                if ($tableRate->getMinAmount() && $tableRate->getMinAmount() > 0) {
+                    continue;
+                }
+
                 [, $sellerId] = explode('|', $sellerKey);
                 $totalPrice += $tableRate->getShippingPrice();
                 $minLeadTime = $minLeadTime === null ? $tableRate->getTotalLeadTime() : min($minLeadTime, $tableRate->getTotalLeadTime());
diff --git a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
index 5369590c3..7c24e25d2 100644
--- a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
+++ b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
@@ -215,6 +215,22 @@ class ShipTableRates extends AbstractModel implements ShipTableRatesInterface
         return $this->setData(self::FREE_SHIPPING, $freeShipping);
     }
 
+    /**
+     * @inheritDoc
+     */
+    public function getMinOrderAmount(): ?float
+    {
+        return (float)$this->getData(self::MIN_ORDER_AMOUNT);
+    }
+
+    /**
+     * @inheritDoc
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface
+    {
+        return $this->setData(self::MIN_ORDER_AMOUNT, $minOrderAmount);
+    }
+
     /**
      * Override to Converts countries array to string
      * @param $key
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
index 4043e1b79..ea096611c 100755
--- a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
@@ -15,6 +15,8 @@ use Magento\Quote\Model\Quote\Address\Total;
 use Magento\Quote\Model\Quote\Address\Total\Shipping;
 use Coditron\CustomShippingRate\Helper\Data;
 use Coditron\CustomShippingRate\Model\Carrier;
+use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
+use Magento\Quote\Model\Quote\Address\RateFactory;
 
 class ShippingPlugin
 {
@@ -23,13 +25,29 @@ class ShippingPlugin
      */
     protected $customShippingRateHelper;
 
+    /**
+     * @var ShipTableRatesRepositoryInterface
+     */
+    protected $shipTableRatesRepository;
+
+    /**
+     * @var RateFactory
+     */
+    protected $rateFactory;
+
     /**
      * @param Data $customShippingRateHelper
+     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
+     * @param RateFactory $rateFactory
      */
     public function __construct(
-        Data $customShippingRateHelper
+        Data $customShippingRateHelper,
+        ShipTableRatesRepositoryInterface $shipTableRatesRepository,
+        RateFactory $rateFactory
     ) {
         $this->customShippingRateHelper = $customShippingRateHelper;
+        $this->shipTableRatesRepository = $shipTableRatesRepository;
+        $this->rateFactory = $rateFactory;
     }
 
     /**
@@ -54,7 +72,7 @@ class ShippingPlugin
 
         if (!$this->customShippingRateHelper->isEnabled($storeId)
             || $address->getAddressType() != Address::ADDRESS_TYPE_SHIPPING
-            || strpos((string) $method, Carrier::CODE) === false
+            || (strpos((string) $method, Carrier::CODE) === false && strpos((string) $method, 'freeshipping_threshold_') === false && strpos((string) $method, '_free') === false)
         ) {
             return $proceed($quote, $shippingAssignment, $total);
         }
@@ -65,7 +83,22 @@ class ShippingPlugin
             //update shipping code
             $shipping->setMethod($customShippingOption['code']);
             $address->setShippingMethod($customShippingOption['code']);
+
+            // For free shipping thresholds, create a fake rate if it doesn't exist
+            if (strpos($method, 'freeshipping_threshold_') === 0) {
+                $this->ensureFreeShippingThresholdRate($address, $customShippingOption);
+            } elseif (strpos($method, '_free') !== false) {
+                $this->ensureFreeShippingRate($address, $customShippingOption);
+            }
+
             $this->updateCustomRate($address, $customShippingOption);
+
+            // For free shipping thresholds, ensure totals are recalculated
+            if (strpos($method, 'freeshipping_threshold_') === 0 || strpos($method, '_free') !== false) {
+                $total->setShippingAmount(0);
+                $total->setBaseShippingAmount(0);
+                $total->setShippingDescription($customShippingOption['description']);
+            }
         }
 
         return $proceed($quote, $shippingAssignment, $total);
@@ -108,11 +141,48 @@ class ShippingPlugin
                 $rate = $selectedRate->getPrice();
             }
 
-            $jsonToArray = [
-                'code' => $json,
-                'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
-                'rate' => $rate
-            ];
+            // Handle free shipping threshold codes
+            if (strpos($json, 'freeshipping_threshold_') === 0) {
+                $description = 'Free Shipping';
+                $thresholdId = str_replace('freeshipping_threshold_', '', $json);
+
+                try {
+                    if (is_numeric($thresholdId)) {
+                        $threshold = $this->shipTableRatesRepository->get((int)$thresholdId);
+                        $description = $threshold->getCourier() ?: 'Free Shipping';
+                    }
+                } catch (\Exception $e) {
+                    // Use default description if threshold not found
+                }
+
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => 'freeshipping_threshold',
+                    'rate' => 0, // Free shipping
+                    'description' => $description
+                ];
+            } elseif (strpos($json, '_free') !== false) {
+                // Handle _free suffix codes (e.g., customshippingrate_express_free)
+                $baseCode = str_replace('_free', '', $json);
+                if ($selectedRate = $this->getSelectedShippingRate($address, $baseCode)) {
+                    $description = $selectedRate->getMethodTitle() . ' (Free Shipping)';
+                } else {
+                    $description = 'Free Shipping';
+                }
+
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => 'free_shipping',
+                    'rate' => 0, // Free shipping
+                    'description' => $description
+                ];
+            } else {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
+                    'rate' => $rate
+                ];
+            }
 
             return $this->formatShippingArray($jsonToArray);
         }
@@ -147,6 +217,77 @@ class ShippingPlugin
         return $selectedRate;
     }
 
+    /**
+     * Create a fake shipping rate for free shipping thresholds if it doesn't exist
+     *
+     * @param $address
+     * @param $customShippingOption
+     */
+    protected function ensureFreeShippingThresholdRate($address, $customShippingOption)
+    {
+        $code = $customShippingOption['code'];
+
+        // Check if rate already exists
+        if ($this->getSelectedShippingRate($address, $code)) {
+            return;
+        }
+
+        // Extract threshold ID from code (freeshipping_threshold_123)
+        $thresholdId = str_replace('freeshipping_threshold_', '', $code);
+        $methodTitle = 'Free Shipping';
+
+        try {
+            if (is_numeric($thresholdId)) {
+                $threshold = $this->shipTableRatesRepository->get((int)$thresholdId);
+                $methodTitle = $threshold->getCourier() ?: 'Free Shipping';
+            }
+        } catch (\Exception $e) {
+            // Use default title if threshold not found
+        }
+
+        // Create a fake rate for the free shipping threshold
+        $rate = $this->rateFactory->create();
+        $rate->setCode($code);
+        $rate->setCarrier('customfreeshipping');
+        $rate->setCarrierTitle('Free Shipping');
+        $rate->setMethod('threshold');
+        $rate->setMethodTitle($methodTitle);
+        $rate->setPrice(0);
+        $rate->setCost(0);
+
+        // Add the rate to the address
+        $address->addShippingRate($rate);
+    }
+
+    /**
+     * Create a fake shipping rate for _free suffix codes if it doesn't exist
+     *
+     * @param $address
+     * @param $customShippingOption
+     */
+    protected function ensureFreeShippingRate($address, $customShippingOption)
+    {
+        $code = $customShippingOption['code'];
+
+        // Check if rate already exists
+        if ($this->getSelectedShippingRate($address, $code)) {
+            return;
+        }
+
+        // Create a fake rate for the free shipping
+        $rate = $this->rateFactory->create();
+        $rate->setCode($code);
+        $rate->setCarrier('customfreeshipping');
+        $rate->setCarrierTitle('Free Shipping');
+        $rate->setMethod('free');
+        $rate->setMethodTitle($customShippingOption['description'] ?: 'Free Shipping');
+        $rate->setPrice(0);
+        $rate->setCost(0);
+
+        // Add the rate to the address
+        $address->addShippingRate($rate);
+    }
+
     /**
      * @param $jsonToArray array
      * @return array
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
index 12f42277c..b76c8f100 100644
--- a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
@@ -55,6 +55,9 @@ class ShippingRateListDataProvider extends \Magento\Ui\DataProvider\AbstractData
         $this->collection->addFieldToFilter(
             'seller_id',
             ['eq' => $this->helper->getSellerId()]
+        )->addFieldToFilter(
+            'min_order_amount',
+            ['eq' => 0]
         );
     }
 }
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
index 68559032e..f1710aec1 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
@@ -21,6 +21,7 @@
         <column name="weight" nullable="true" xsi:type="varchar" comment="Weight (Up to, in Kg)" length="255"/>
         <column name="shipping_price" nullable="true" xsi:type="varchar" comment="Shipping price" length="255"/>
         <column name="free_shipping" nullable="false" xsi:type="boolean" default="false" comment="Free Shipping"/>
+        <column name="min_order_amount" nullable="true" xsi:type="decimal" precision="12" scale="4" default="0.0000" comment="Minimum Order Amount for Free Shipping"/>
 		<column name="seller_id" xsi:type="int" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Seller Id"/>
 		<constraint xsi:type="foreign" referenceId="FK_CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" table="coditron_customshippingrate_shiptablerates" column="seller_id" referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
         <index referenceId="CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" indexType="btree">
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
index 3d5012378..c9b75af9f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
@@ -22,6 +22,7 @@
             "weight": true,
             "shipping_price": true,
             "free_shipping": true,
+            "min_order_amount": true,
             "seller_id": true
         },
         "index": {
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
index 63e68d639..ef8d8513f 100755
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
@@ -16,6 +16,15 @@
 $taxHelper = $block->getData('taxHelper');
 ?>
 <?php $_shippingRateGroups = $block->getGroupShippingRates(); ?>
+<?php
+// Debug shipping rate groups
+$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
+$logger = $objectManager->get('\Psr\Log\LoggerInterface');
+$logger->info('Shipping Rate Groups: ' . print_r($_shippingRateGroups, true));
+?>
+<script>
+console.log('Shipping Rate Groups:', <?= json_encode($_shippingRateGroups) ?>);
+</script>
 <?php if ($_shippingRateGroups): ?>
     <div id="order-shipping-method-choose" class="control">
         <dl class="admin__order-shipment-methods">
@@ -66,6 +75,45 @@ $taxHelper = $block->getData('taxHelper');
                 </ul>
             </dd>
         <?php endforeach; ?>
+
+        <?php /*------- Start Free Shipping Thresholds --------*/ ?>
+        <?php $freeShippingThresholds = $block->getFreeShippingThresholds(); ?>
+        <?php if (!empty($freeShippingThresholds)): ?>
+            <dt class="admin__order-shipment-methods-title">Free Shipping</dt>
+            <dd class="admin__order-shipment-methods-options">
+                <ul class="admin__order-shipment-methods-options-list">
+                    <?php foreach ($freeShippingThresholds as $threshold): ?>
+                        <?php if ($threshold['min_amount'] > 0): ?>
+                            <?php
+                            $thresholdCode = 'freeshipping_threshold_' . $threshold['id'];
+                            $isChecked = $block->isMethodActive($thresholdCode) ? 'checked="checked"' : '';
+                            ?>
+                            <li class="admin__field-option">
+                                <input name="order[shipping_method]" type="radio"
+                                       value="<?= $block->escapeHtmlAttr($thresholdCode) ?>"
+                                       id="s_method_<?= $block->escapeHtmlAttr($thresholdCode) ?>"
+                                       <?= $isChecked ?>
+                                       class="admin__control-radio required-entry"/>
+                                <label class="admin__field-label" for="s_method_<?= $block->escapeHtmlAttr($thresholdCode) ?>">
+                                    <?= $block->escapeHtml($threshold['courier']) ?> -
+                                    <strong>
+                                        <?= $block->escapeHtml(__('$0.00')) ?>
+                                    </strong><br/>
+                                    <span style="color: #999; font-size: 12px;"><?= $block->escapeHtml(__('For Order over: $%1', $threshold['min_amount'])) ?></span>
+                                </label>
+                                <?= /* @noEscape */ $secureRenderer->renderEventListenerAsTag(
+                                    'onclick',
+                                    "order.setShippingMethod(this.value)",
+                                    'input#s_method_' . $block->escapeHtmlAttr($thresholdCode)
+                                ) ?>
+                            </li>
+                        <?php endif; ?>
+                    <?php endforeach; ?>
+                </ul>
+            </dd>
+        <?php endif; ?>
+        <?php /*------- End Free Shipping Thresholds --------*/ ?>
+
             <?php /*------- Start Coditron --------*/ ?>
             <?php if ($this->helper('Coditron\CustomShippingRate\Helper\Data')->isEnabled($this->getQuote()->getStore()->getStoreId())): ?>
                 <dt class="admin__order-shipment-methods-title">
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml
index 5d24fa8dc..756abb5ca 100644
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_form.xml
@@ -107,6 +107,23 @@
 				</validation>
 			</settings>
 		</field>
+		<field name="min_order_amount" formElement="input" sortOrder="45">
+			<argument name="data" xsi:type="array">
+				<item name="config" xsi:type="array">
+					<item name="source" xsi:type="string">ShipTableRates</item>
+					<item name="notice" xsi:type="string" translate="true">Minimum order amount for free shipping (USD)</item>
+				</item>
+			</argument>
+			<settings>
+				<dataType>text</dataType>
+				<label translate="true">Min Order Amount</label>
+				<dataScope>min_order_amount</dataScope>
+				<validation>
+					<rule name="validate-number" xsi:type="boolean">true</rule>
+					<rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
+				</validation>
+			</settings>
+		</field>
 		<field name="seller_id" formElement="input" sortOrder="50">
 			<argument name="data" xsi:type="array">
 				<item name="config" xsi:type="array">
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml
index 81b68846f..b3291de20 100644
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/ui_component/coditron_customshippingrate_shiptablerates_listing.xml
@@ -136,6 +136,19 @@
 				</editor>
 			</settings>
 		</column>
+		<column name="min_order_amount">
+			<settings>
+				<filter>text</filter>
+				<label translate="true">Min Order Amount</label>
+				<editor>
+					<editorType>text</editorType>
+					<validation>
+						<rule name="validate-number" xsi:type="boolean">true</rule>
+						<rule name="validate-zero-or-greater" xsi:type="boolean">true</rule>
+					</validation>
+				</editor>
+			</settings>
+		</column>
 		<actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\ShipTableRatesActions">
 			<settings>
 				<indexField>shiptablerates_id</indexField>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
index 972c401ce..d8c3820b3 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
@@ -16,10 +16,11 @@
             </action>
         </referenceBlock>
         <referenceContainer name="seller.content">
-            <block class="Magento\Framework\View\Element\Template" name="sellership_rate_manage" template="Coditron_CustomShippingRate::shiprate/list.phtml" cacheable="false"></block>
+            <block class="Coditron\CustomShippingRate\Block\TabbedRates" name="sellership_rate_manage" template="Coditron_CustomShippingRate::shiprate/tabbed_list.phtml" cacheable="false"></block>
         </referenceContainer>
         <referenceContainer name="sellership_rate_manage">
             <uiComponent name="sellership_rates_list_front"/>
+            <uiComponent name="sellership_threshold_list_front"/>
         </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
index 3894ab393..d90854804 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
@@ -1,7 +1,7 @@
 <?php
      $marketplaceHelper = $block->getMpHelper();
      $isPartner= $marketplaceHelper->isSeller();
-     $isEnable = false;
+     $isEnable = true;
      $magentoCurrentUrl = $block->getCurrentUrl();
 ?>
 <?php if ($isPartner): ?>
